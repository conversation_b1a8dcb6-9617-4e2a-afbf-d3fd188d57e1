# Qilin推理引擎开发提示词集合 🦄

本目录包含了Qilin推理引擎后续开发的详细提示词，每个提示词对应一个具体的开发任务。

Qilin (麒麟) - 中国神话中的瑞兽，象征智慧与祥瑞，正如这个推理引擎为AI应用带来的智慧与好运。

## 📋 提示词列表

### 01. 完善张量操作系统 (`01_tensor_operations.md`)
**优先级**: 🔴 最高
**预计工期**: 1-2周
**依赖**: 无

扩展张量系统，实现更多数学函数、归约操作和高级张量操作。包括三角函数、指数对数、统计归约、广播机制、张量拼接等核心功能。

**关键组件**:
- 数学函数扩展 (sin, cos, exp, log等)
- 归约操作 (sum, mean, max, min等)
- 高级张量操作 (cat, stack, gather等)
- 内存优化和SIMD加速

---

### 02. 实现注意力机制 (`02_attention_mechanism.md`)
**优先级**: 🔴 最高
**预计工期**: 2-3周
**依赖**: 01_tensor_operations

实现Transformer的核心注意力机制，包括缩放点积注意力、多头注意力和KV缓存系统。

**关键组件**:
- 缩放点积注意力算法
- 多头注意力并行计算
- KV缓存管理系统
- 各种掩码支持 (因果掩码、填充掩码)

---

### 03. 实现神经网络层 (`03_neural_network_layers.md`)
**优先级**: 🔴 最高
**预计工期**: 2-3周
**依赖**: 01_tensor_operations

实现Transformer所需的各种神经网络层，包括线性层、归一化层、激活函数和嵌入层。

**关键组件**:
- 线性层 (权重初始化、偏置支持)
- 归一化层 (LayerNorm, RMSNorm等)
- 激活函数层 (ReLU, GELU, SwiGLU等)
- 嵌入层 (词嵌入、位置编码)

---

### 04. 实现Transformer块 (`04_transformer_blocks.md`)
**优先级**: 🟡 高
**预计工期**: 2-3周
**依赖**: 02_attention_mechanism, 03_neural_network_layers

组合注意力机制和神经网络层，构建完整的Transformer编码器和解码器块。

**关键组件**:
- Transformer编码器层
- Transformer解码器层
- 残差连接和层归一化
- 配置系统和模型变体支持

---

### 05. 实现模型加载系统 (`05_model_loading.md`)
**优先级**: 🟡 高
**预计工期**: 2-3周
**依赖**: 04_transformer_blocks

建立完整的模型加载和权重管理系统，支持多种权重格式和模型架构。

**关键组件**:
- SafeTensors和PyTorch权重加载
- 权重映射和转换系统
- 模型注册和工厂模式
- HuggingFace兼容性

---

### 06. 实现推理引擎 (`06_inference_engine.md`)
**优先级**: 🟡 高
**预计工期**: 3-4周
**依赖**: 05_model_loading

构建高效的推理引擎，整合所有组件，提供批处理、缓存管理和生成策略。

**关键组件**:
- 核心推理引擎
- 动态批处理系统
- KV缓存管理
- 多种生成策略 (贪心、采样、束搜索)

---

### 07. 实现GPU支持 (`07_gpu_support.md`)
**优先级**: 🟠 中
**预计工期**: 4-6周
**依赖**: 06_inference_engine

添加CUDA和Metal GPU支持，大幅提升推理性能。

**关键组件**:
- CUDA张量和操作实现
- Metal计算着色器
- 跨设备数据传输
- GPU内存管理和优化

---

### 08. 实现Python绑定 (`08_python_bindings.md`)
**优先级**: 🟠 中
**预计工期**: 2-3周
**依赖**: 06_inference_engine

创建Python绑定，让Python用户能够使用Rust推理引擎。

**关键组件**:
- PyO3绑定实现
- NumPy和PyTorch集成
- 异步支持
- 包构建和分发

---

## 🗓️ 开发时间线

### 第一阶段 (1-2个月) - 核心功能
- ✅ 项目架构设计 (已完成)
- 🔄 01_tensor_operations (1-2周)
- 🔄 02_attention_mechanism (2-3周)
- 🔄 03_neural_network_layers (2-3周)

### 第二阶段 (2-3个月) - 模型和推理
- 🔄 04_transformer_blocks (2-3周)
- 🔄 05_model_loading (2-3周)
- 🔄 06_inference_engine (3-4周)

### 第三阶段 (3-4个月) - 性能和生态
- 🔄 07_gpu_support (4-6周)
- 🔄 08_python_bindings (2-3周)

## 📊 依赖关系图

```
01_tensor_operations
    ↓
02_attention_mechanism ← 03_neural_network_layers
    ↓                           ↓
    04_transformer_blocks ←-----┘
            ↓
    05_model_loading
            ↓
    06_inference_engine
            ↓
07_gpu_support    08_python_bindings
```

## 🎯 成功指标

### 性能目标
- **吞吐量**: 125M模型 >2,500 tokens/s
- **延迟**: 小模型 <5ms 首token延迟
- **内存**: 高效的内存使用，支持大模型

### 功能目标
- **模型支持**: GPT、BERT、T5等主流架构
- **格式支持**: SafeTensors、PyTorch、GGUF
- **平台支持**: CPU、CUDA、Metal
- **语言支持**: Rust原生 + Python绑定

### 质量目标
- **测试覆盖**: >90%代码覆盖率
- **文档完整**: 所有公开API有文档
- **性能基准**: 与主流框架对比
- **内存安全**: 零内存泄漏

## 🛠️ 使用说明

1. **选择提示词**: 根据当前开发阶段选择对应的提示词文件
2. **阅读背景**: 了解任务背景和依赖关系
3. **按需实现**: 根据优先级和资源情况实现功能
4. **测试验证**: 确保每个阶段都通过测试验证
5. **文档更新**: 及时更新文档和示例

## 📝 注意事项

- **严格按依赖顺序**: 确保前置任务完成后再开始后续任务
- **保持代码质量**: 每个阶段都要有完整的测试和文档
- **性能优先**: 在功能正确的基础上持续优化性能
- **向后兼容**: 新功能不应破坏现有接口
- **社区反馈**: 及时收集和响应用户反馈

## 🤝 贡献指南

1. 选择一个提示词任务
2. 创建对应的功能分支
3. 按照提示词要求实现功能
4. 编写完整的测试用例
5. 更新相关文档
6. 提交Pull Request

每个提示词都是独立的开发任务，可以并行开发或按优先级顺序实现。
